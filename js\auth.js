// Simple Authentication System
class AuthSystem {
    constructor() {
        this.currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
        this.sessionTimeout = 8 * 60 * 60 * 1000; // 8 hours
        this.validCredentials = {
            username: 'HASHISH',
            password: 'HASHISH121233'
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkSession();
    }

    setupEventListeners() {
        // Form submission
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
    }

    async handleLogin() {
        const username = document.getElementById('login-username').value.trim();
        const password = document.getElementById('login-password').value;
        const rememberMe = document.getElementById('remember-me').checked;

        if (!username || !password) {
            this.showNotification('يرجى ملء جميع الحقول', 'error');
            return;
        }

        this.showLoading(true);

        // Simulate API call delay
        await this.delay(1000);

        if (this.authenticateUser(username, password)) {
            this.loginUser(rememberMe);
            this.showNotification('مرحباً بك! جاري تحويلك للنظام...', 'success');

            // Redirect to main app
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        } else {
            this.showNotification('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
        }

        this.showLoading(false);
    }

    authenticateUser(username, password) {
        return username === this.validCredentials.username &&
               password === this.validCredentials.password;
    }

    loginUser(rememberMe) {
        const sessionData = {
            id: 'owner-001',
            username: 'HASHISH',
            fullName: 'صاحب المحل',
            role: 'owner',
            permissions: ['all'],
            loginTime: new Date().toISOString(),
            expiresAt: new Date(Date.now() + this.sessionTimeout).toISOString()
        };

        localStorage.setItem('currentUser', JSON.stringify(sessionData));

        if (rememberMe) {
            localStorage.setItem('rememberUser', this.validCredentials.username);
        }

        this.currentUser = sessionData;
    }



    checkSession() {
        if (this.currentUser) {
            const expiresAt = new Date(this.currentUser.expiresAt);
            if (new Date() > expiresAt) {
                this.logout();
                this.showNotification('انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى', 'warning');
            } else {
                // User is already logged in, redirect to main app
                window.location.href = 'index.html';
            }
        }

        // Check if user should be remembered
        const rememberUser = localStorage.getItem('rememberUser');
        if (rememberUser) {
            document.getElementById('login-username').value = rememberUser;
            document.getElementById('remember-me').checked = true;
        }
    }
    }

    logout() {
        localStorage.removeItem('currentUser');
        this.currentUser = null;
    }

    updateLastLogin(userId) {
        const userIndex = this.users.findIndex(user => user.id === userId);
        if (userIndex !== -1) {
            this.users[userIndex].lastLogin = new Date().toISOString();
            this.saveUsers();
        }
    }

    saveUsers() {
        localStorage.setItem('users', JSON.stringify(this.users));
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        overlay.style.display = show ? 'flex' : 'none';
    }

    showNotification(message, type = 'success') {
        const notification = document.getElementById('notification');
        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');

        // Set icon based on type
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle'
        };

        icon.className = `notification-icon ${icons[type]}`;
        messageEl.textContent = message;
        notification.className = `notification ${type}`;

        // Show notification
        notification.classList.add('show');

        // Hide after 4 seconds
        setTimeout(() => {
            notification.classList.remove('show');
        }, 4000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Password toggle function
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.parentElement.querySelector('.toggle-password i');
    
    if (input.type === 'password') {
        input.type = 'text';
        button.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        button.className = 'fas fa-eye';
    }
}

// Initialize authentication system
const auth = new AuthSystem();
