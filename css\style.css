/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%, 300% 300%, 200% 200%, 400% 400%;
    animation:
        gradientShift 20s ease infinite,
        floatingOrbs 30s ease-in-out infinite;
    min-height: 100vh;
    color: #333;
    direction: rtl;
    position: relative;
    overflow-x: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Floating particles background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 100px 100px, 150px 150px, 80px 80px;
    animation: float 30s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-20px) translateX(10px); }
    66% { transform: translateY(-10px) translateX(-10px); }
    100% { transform: translateY(0px) translateX(0px); }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Floating Header Styles */
.floating-header {
    position: fixed;
    top: 20px;
    left: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow:
        0 10px 40px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    z-index: 1000;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.floating-header:hover {
    box-shadow:
        0 15px 50px rgba(0, 0, 0, 0.15),
        0 6px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    gap: 2rem;
}

/* Logo Section */
.header-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.logo-text h1 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
    margin: 0;
    line-height: 1.2;
}

.logo-subtitle {
    font-size: 0.8rem;
    color: #666;
    font-weight: 400;
}

/* Mega Menu Trigger */
.mega-menu-trigger {
    flex: 1;
    display: flex;
    justify-content: center;
}

.menu-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 15px;
    padding: 0.75rem 2rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.menu-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.menu-btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.menu-btn:hover::before {
    left: 100%;
}

.menu-icon {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.menu-icon span {
    width: 20px;
    height: 2px;
    background: white;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.menu-btn:hover .menu-icon span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.menu-btn:hover .menu-icon span:nth-child(2) {
    opacity: 0;
}

.menu-btn:hover .menu-icon span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
}

.quick-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #667eea;
    border-radius: 12px;
    padding: 0.5rem 1rem;
    color: #667eea;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.quick-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Header User */
.header-user {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
}

.user-avatar {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.user-role {
    font-size: 0.8rem;
    color: #666;
}

.user-menu-btn {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.user-menu-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: rotate(180deg);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0.75rem 1rem;
    color: #333;
    text-decoration: none;
    transition: background 0.3s ease;
    font-weight: 500;
}

.dropdown-item:hover {
    background: #f8f9fa;
}

.dropdown-divider {
    height: 1px;
    background: #e9ecef;
    margin: 0.5rem 0;
}

/* Mega Menu Overlay */
.mega-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.mega-menu-overlay.show {
    opacity: 1;
    visibility: visible;
}

.mega-menu-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    width: 90%;
    max-width: 1200px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow:
        0 25px 60px rgba(0, 0, 0, 0.2),
        0 10px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.mega-menu-overlay.show .mega-menu-container {
    transform: translate(-50%, -50%) scale(1);
}

.mega-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 3rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

.mega-menu-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.close-mega-menu {
    background: #dc3545;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-mega-menu:hover {
    background: #c82333;
    transform: rotate(90deg) scale(1.1);
}

.mega-menu-content {
    padding: 2rem 3rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.menu-section {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 20px;
    padding: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.menu-section:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #667eea;
}

.section-title i {
    color: #667eea;
    font-size: 1.2rem;
}

.menu-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.menu-item:hover {
    background: rgba(102, 126, 234, 0.05);
    border-color: #667eea;
    transform: translateX(5px);
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.15);
}

.menu-item:hover::before {
    left: 100%;
}

.menu-item.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.menu-item.active .item-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.item-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.menu-item:hover .item-icon {
    transform: scale(1.1) rotate(5deg);
}

.item-content h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    transition: color 0.3s ease;
}

.item-content p {
    font-size: 0.85rem;
    color: #666;
    margin: 0;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.menu-item:hover .item-content p {
    color: #555;
}

.menu-item.active .item-content p {
    color: rgba(255, 255, 255, 0.8);
}

/* Main Content */
.main {
    padding: 120px 20px 2rem 20px;
    margin-top: 0;
}

.section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-title {
    font-size: 2rem;
    color: white;
    margin-bottom: 2rem;
    text-align: center;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.stat-card:hover::before {
    left: 100%;
}

.stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.stat-info p {
    color: #666;
    font-weight: 500;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-family: inherit;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:active {
    transform: translateY(-1px) scale(0.98);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Search and Filter */
.search-filter {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.search-box {
    position: relative;
    flex: 1;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

#category-filter {
    padding: 0.75rem 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    min-width: 200px;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.product-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(0, 0, 0, 0.1);
}

.product-card:hover::before {
    transform: scaleX(1);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: #f8f9fa;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.product-category {
    color: #667eea;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 1rem;
}

.product-quantity {
    color: #666;
    margin-bottom: 1rem;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Recent Activities */
.recent-activities {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.recent-activities h3 {
    margin-bottom: 1.5rem;
    color: #333;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-content {
    flex: 1;
}

.activity-text {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #666;
    font-size: 0.9rem;
}

/* Tables */
.sales-table-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.sales-table {
    width: 100%;
    border-collapse: collapse;
}

.sales-table th,
.sales-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #eee;
}

.sales-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.sales-table tr:hover {
    background: #f8f9fa;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    margin: 5% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.25),
        0 10px 20px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.1),
        0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 1);
}

#product-form {
    padding: 2rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
}

/* Offers Grid */
.offers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.offer-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.offer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.offer-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.offer-discount {
    font-size: 2rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 1rem;
}

.offer-description {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.offer-validity {
    color: #dc3545;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.offer-actions {
    display: flex;
    gap: 0.5rem;
}

/* Charts */
.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.chart-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.chart-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
    text-align: center;
}

/* Report Filters */
.report-filters {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    backdrop-filter: blur(10px);
}

.report-filters select,
.report-filters input {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
}

/* Loading Spinner */
.loading {
    display: none;
    position: fixed;
    z-index: 3000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
}

.spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Product Select Items */
.product-select-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.product-select-item:last-child {
    border-bottom: none;
}

.product-select-item input[type="checkbox"] {
    width: auto;
}

.product-select-item label {
    flex: 1;
    margin-bottom: 0;
    cursor: pointer;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 3000;
    animation: slideInRight 0.3s ease-out;
}

.notification.error {
    background: #dc3545;
}

.notification.warning {
    background: #ffc107;
    color: #333;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* User Dropdown */
.user-dropdown {
    position: relative;
}

.user-btn {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #667eea;
    font-weight: 500;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.user-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.user-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0.75rem 1rem;
    color: #333;
    text-decoration: none;
    transition: background 0.3s ease;
}

.user-menu a:hover {
    background: #f8f9fa;
}

/* Settings Styles */
.settings-nav {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.95);
    padding: 1rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    flex-wrap: wrap;
}

.settings-tab-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
    font-family: inherit;
}

.settings-tab-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.settings-content {
    display: none;
}

.settings-content.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

.settings-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.settings-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.3rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.category-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.category-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.category-name {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.category-actions {
    display: flex;
    gap: 0.5rem;
}

.category-description {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.category-stats {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #666;
}

/* Users Table */
.users-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #eee;
}

.users-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.users-table tr:hover {
    background: #f8f9fa;
}

.user-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.user-status.active {
    background: #d4edda;
    color: #155724;
}

.user-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Backup Actions */
.backup-actions {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.backup-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.backup-info h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.backup-info p {
    color: #666;
    margin: 0;
}

.text-danger {
    color: #dc3545 !important;
}

.import-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Color Picker */
.color-picker-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.color-presets {
    display: flex;
    gap: 0.5rem;
}

.color-preset {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-preset:hover {
    transform: scale(1.1);
    border-color: #667eea;
}

/* Enhanced Offer Styles */
.offer-scope {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.radio-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.radio-container input {
    display: none;
}

.radio-mark {
    width: 20px;
    height: 20px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    margin-left: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.radio-container input:checked ~ .radio-mark {
    border-color: #667eea;
    background: #667eea;
}

.radio-mark:after {
    content: "";
    position: absolute;
    display: none;
    left: 50%;
    top: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
    transform: translate(-50%, -50%);
}

.radio-container input:checked ~ .radio-mark:after {
    display: block;
}

.form-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
    display: block;
}

/* Enhanced Offer Cards */
.offer-card {
    position: relative;
    overflow: hidden;
}

.offer-card.featured::before {
    content: 'مميز';
    position: absolute;
    top: 15px;
    left: -30px;
    background: linear-gradient(45deg, #ffc107, #ff8c00);
    color: white;
    padding: 5px 40px;
    font-size: 0.8rem;
    font-weight: 600;
    transform: rotate(-45deg);
    z-index: 1;
}

.offer-type-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.offer-type-percentage {
    background: #e3f2fd;
    color: #1976d2;
}

.offer-type-fixed {
    background: #e8f5e8;
    color: #2e7d32;
}

.offer-type-buy-get {
    background: #fff3e0;
    color: #f57c00;
}

.offer-type-bundle {
    background: #f3e5f5;
    color: #7b1fa2;
}

.offer-priority {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.offer-priority.low {
    background: #28a745;
}

.offer-priority.medium {
    background: #ffc107;
}

.offer-priority.high {
    background: #fd7e14;
}

.offer-priority.urgent {
    background: #dc3545;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.offer-usage {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.5rem;
}

.offer-progress {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.offer-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}

/* Print Styles */
@media print {
    body * {
        visibility: hidden;
    }

    .print-area, .print-area * {
        visibility: visible;
    }

    .print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }

    .no-print {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-container {
        padding: 1rem 1.5rem;
        gap: 1rem;
    }

    .quick-actions {
        display: none;
    }

    .mega-menu-content {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        padding: 1.5rem 2rem;
    }
}

@media (max-width: 768px) {
    .floating-header {
        top: 10px;
        left: 10px;
        right: 10px;
    }

    .header-container {
        padding: 0.75rem 1rem;
        gap: 0.5rem;
    }

    .logo-text h1 {
        font-size: 1.1rem;
    }

    .logo-subtitle {
        display: none;
    }

    .user-info {
        display: none;
    }

    .menu-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .menu-text {
        display: none;
    }

    .mega-menu-container {
        width: 95%;
        max-height: 85vh;
    }

    .mega-menu-header {
        padding: 1.5rem 2rem;
    }

    .mega-menu-content {
        grid-template-columns: 1fr;
        padding: 1rem 2rem;
        gap: 1.5rem;
    }

    .main {
        padding: 100px 10px 2rem 10px;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .search-filter {
        flex-direction: column;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .offers-grid {
        grid-template-columns: 1fr;
    }

    .charts-container {
        grid-template-columns: 1fr;
    }

    .report-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .form-actions {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 2% auto;
    }

    .settings-nav {
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .floating-header {
        top: 5px;
        left: 5px;
        right: 5px;
    }

    .header-container {
        padding: 0.5rem 0.75rem;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .logo-text h1 {
        font-size: 1rem;
    }

    .menu-btn {
        padding: 0.5rem 0.75rem;
    }

    .mega-menu-header {
        padding: 1rem 1.5rem;
    }

    .mega-menu-header h2 {
        font-size: 1.4rem;
    }

    .mega-menu-content {
        padding: 1rem 1.5rem;
    }

    .main {
        padding: 90px 5px 1rem 5px;
    }
}
