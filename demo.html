<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض تجريبي - نظام أورجينال فرش السيارات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 3rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .demo-title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .demo-subtitle {
            font-size: 1.3rem;
            color: #666;
            margin-bottom: 2rem;
        }
        
        .demo-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            font-size: 3rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
        }
        
        .demo-actions {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            min-width: 200px;
            justify-content: center;
        }
        
        .demo-btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .demo-btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border: 2px solid #667eea;
            backdrop-filter: blur(10px);
        }
        
        .demo-btn:hover {
            transform: translateY(-3px) scale(1.02);
        }
        
        .demo-info {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            margin-top: 3rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .demo-credentials {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .credentials-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .credential-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-family: 'Courier New', monospace;
        }
        
        .credential-label {
            font-weight: 600;
            color: #667eea;
        }
        
        .credential-value {
            color: #333;
            background: rgba(255, 255, 255, 0.7);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">
                <i class="fas fa-car"></i>
                نظام أورجينال فرش السيارات
            </h1>
            <p class="demo-subtitle">
                نظام إدارة شامل ومتطور لمحلات فرش السيارات مع تصميم احترافي وميزات متقدمة
            </p>
            
            <div class="demo-actions">
                <a href="login.html" class="demo-btn demo-btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    دخول النظام
                </a>
                <a href="#features" class="demo-btn demo-btn-secondary">
                    <i class="fas fa-info-circle"></i>
                    المزيد من المعلومات
                </a>
            </div>
        </div>
        
        <div id="features" class="demo-features">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <h3 class="feature-title">لوحة تحكم متقدمة</h3>
                <p class="feature-description">
                    لوحة تحكم شاملة تعرض جميع الإحصائيات والبيانات المهمة في مكان واحد مع تصميم عصري وتفاعلي
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-box"></i>
                </div>
                <h3 class="feature-title">إدارة المنتجات</h3>
                <p class="feature-description">
                    نظام متكامل لإدارة المنتجات مع فئات احترافية، صور، أسعار، ومتابعة المخزون بطريقة ذكية
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3 class="feature-title">نظام المبيعات</h3>
                <p class="feature-description">
                    تسجيل المبيعات، إنشاء فواتير احترافية، طباعة الفواتير، وتتبع جميع المعاملات التجارية
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <h3 class="feature-title">العروض والخصومات</h3>
                <p class="feature-description">
                    نظام عروض متقدم مع أنواع مختلفة من الخصومات، تحديد فترات الصلاحية، وتطبيق تلقائي للعروض
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3 class="feature-title">التقارير والإحصائيات</h3>
                <p class="feature-description">
                    تقارير مفصلة مع رسوم بيانية تفاعلية لمتابعة الأداء والمبيعات والأرباح بطريقة بصرية واضحة
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <h3 class="feature-title">إعدادات شاملة</h3>
                <p class="feature-description">
                    لوحة إعدادات متكاملة للتحكم في جميع جوانب النظام، المظهر، المستخدمين، والنسخ الاحتياطي
                </p>
            </div>
        </div>
        
        <div class="demo-info">
            <h3>معلومات تجريبية</h3>
            <p>يمكنك تجربة النظام باستخدام البيانات التالية:</p>
            
            <div class="demo-credentials">
                <div class="credentials-title">بيانات تسجيل الدخول:</div>
                <div class="credential-item">
                    <span class="credential-label">اسم المستخدم:</span>
                    <span class="credential-value">admin</span>
                </div>
                <div class="credential-item">
                    <span class="credential-label">كلمة المرور:</span>
                    <span class="credential-value">admin123</span>
                </div>
            </div>
            
            <p style="margin-top: 1rem; color: #666; font-size: 0.9rem;">
                <i class="fas fa-info-circle"></i>
                النظام يحتوي على بيانات تجريبية لتسهيل التجربة والاستكشاف
            </p>
        </div>
    </div>
</body>
</html>
