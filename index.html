<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة أورجينال فرش السيارات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-car"></i>
                <h1>أورجينال فرش السيارات</h1>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="#dashboard" class="nav-link active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                    </a></li>
                    <li><a href="#products" class="nav-link" data-section="products">
                        <i class="fas fa-box"></i> المنتجات
                    </a></li>
                    <li><a href="#sales" class="nav-link" data-section="sales">
                        <i class="fas fa-shopping-cart"></i> المبيعات
                    </a></li>
                    <li><a href="#offers" class="nav-link" data-section="offers">
                        <i class="fas fa-tags"></i> العروض
                    </a></li>
                    <li><a href="#reports" class="nav-link" data-section="reports">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a></li>
                </ul>
            </nav>
            <div class="user-info">
                <span>مرحباً، المدير</span>
                <i class="fas fa-user-circle"></i>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Dashboard Section -->
        <section id="dashboard" class="section active">
            <div class="container">
                <h2 class="section-title">لوحة التحكم</h2>
                
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-products">0</h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-sales">0</h3>
                            <p>إجمالي المبيعات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-revenue">0 ج.م</h3>
                            <p>إجمالي الإيرادات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="active-offers">0</h3>
                            <p>العروض النشطة</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="recent-activities">
                    <h3>النشاطات الأخيرة</h3>
                    <div class="activities-list" id="recent-activities">
                        <!-- Activities will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Section -->
        <section id="products" class="section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">إدارة المنتجات</h2>
                    <button class="btn btn-primary" id="add-product-btn">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </button>
                </div>

                <!-- Search and Filter -->
                <div class="search-filter">
                    <div class="search-box">
                        <input type="text" id="product-search" placeholder="البحث في المنتجات...">
                        <i class="fas fa-search"></i>
                    </div>
                    <select id="category-filter">
                        <option value="">جميع الفئات</option>
                        <option value="seat-covers">أغطية المقاعد</option>
                        <option value="floor-mats">سجاد الأرضية</option>
                        <option value="steering-covers">أغطية المقود</option>
                        <option value="accessories">إكسسوارات</option>
                    </select>
                </div>

                <!-- Products Grid -->
                <div class="products-grid" id="products-grid">
                    <!-- Products will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Sales Section -->
        <section id="sales" class="section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">إدارة المبيعات</h2>
                    <button class="btn btn-primary" id="new-sale-btn">
                        <i class="fas fa-plus"></i> عملية بيع جديدة
                    </button>
                </div>

                <!-- Sales List -->
                <div class="sales-table-container">
                    <table class="sales-table" id="sales-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>العميل</th>
                                <th>المنتجات</th>
                                <th>المبلغ الإجمالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Sales will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Offers Section -->
        <section id="offers" class="section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">إدارة العروض</h2>
                    <button class="btn btn-primary" id="add-offer-btn">
                        <i class="fas fa-plus"></i> إضافة عرض جديد
                    </button>
                </div>

                <!-- Offers Grid -->
                <div class="offers-grid" id="offers-grid">
                    <!-- Offers will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports" class="section">
            <div class="container">
                <h2 class="section-title">التقارير والإحصائيات</h2>
                
                <!-- Report Filters -->
                <div class="report-filters">
                    <select id="report-type">
                        <option value="sales">تقرير المبيعات</option>
                        <option value="products">تقرير المنتجات</option>
                        <option value="revenue">تقرير الإيرادات</option>
                    </select>
                    <input type="date" id="date-from">
                    <input type="date" id="date-to">
                    <button class="btn btn-secondary" id="generate-report">إنشاء التقرير</button>
                </div>

                <!-- Charts Container -->
                <div class="charts-container">
                    <div class="chart-card">
                        <h3>مبيعات الشهر</h3>
                        <canvas id="sales-chart"></canvas>
                    </div>
                    <div class="chart-card">
                        <h3>أفضل المنتجات مبيعاً</h3>
                        <canvas id="products-chart"></canvas>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <!-- Product Modal -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="product-modal-title">إضافة منتج جديد</h3>
                <span class="close" id="close-product-modal">&times;</span>
            </div>
            <form id="product-form">
                <div class="form-group">
                    <label for="product-name">اسم المنتج</label>
                    <input type="text" id="product-name" required>
                </div>
                <div class="form-group">
                    <label for="product-category">الفئة</label>
                    <select id="product-category" required>
                        <option value="">اختر الفئة</option>
                        <option value="seat-covers">أغطية المقاعد</option>
                        <option value="floor-mats">سجاد الأرضية</option>
                        <option value="steering-covers">أغطية المقود</option>
                        <option value="accessories">إكسسوارات</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="product-price">السعر</label>
                    <input type="number" id="product-price" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="product-quantity">الكمية</label>
                    <input type="number" id="product-quantity" required>
                </div>
                <div class="form-group">
                    <label for="product-description">الوصف</label>
                    <textarea id="product-description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="product-image">صورة المنتج</label>
                    <input type="file" id="product-image" accept="image/*">
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary" id="cancel-product">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loading" class="loading">
        <div class="spinner"></div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
