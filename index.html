<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة أورجينال فرش السيارات</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Floating Header -->
    <header class="floating-header" id="floating-header">
        <div class="header-container">
            <!-- Logo Section -->
            <div class="header-logo">
                <div class="logo-icon">
                    <i class="fas fa-car"></i>
                </div>
                <div class="logo-text">
                    <h1>أورجينال فرش السيارات</h1>
                    <span class="logo-subtitle">نظام الإدارة الاحترافي</span>
                </div>
            </div>

            <!-- Mega Menu Button -->
            <div class="mega-menu-trigger">
                <button class="menu-btn" id="mega-menu-btn">
                    <span class="menu-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                    <span class="menu-text">القائمة الرئيسية</span>
                </button>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="quick-btn" id="quick-sale-btn" title="بيع سريع">
                    <i class="fas fa-plus-circle"></i>
                    <span>بيع سريع</span>
                </button>
                <button class="quick-btn" id="quick-product-btn" title="إضافة منتج">
                    <i class="fas fa-box"></i>
                    <span>منتج جديد</span>
                </button>
            </div>

            <!-- User Section -->
            <div class="header-user">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-info">
                    <span class="user-name" id="current-user-name">صاحب المحل</span>
                    <span class="user-role">المدير العام</span>
                </div>
                <button class="user-menu-btn" id="user-menu-btn">
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="user-dropdown" id="user-menu">
                    <a href="#" class="dropdown-item" id="profile-link">
                        <i class="fas fa-user"></i> الملف الشخصي
                    </a>
                    <a href="#" class="dropdown-item" id="settings-link">
                        <i class="fas fa-cog"></i> الإعدادات
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item" id="logout-link">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Mega Menu Overlay -->
    <div class="mega-menu-overlay" id="mega-menu-overlay">
        <div class="mega-menu-container">
            <div class="mega-menu-header">
                <h2>القائمة الرئيسية</h2>
                <button class="close-mega-menu" id="close-mega-menu">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mega-menu-content">
                <div class="menu-section">
                    <h3 class="section-title">
                        <i class="fas fa-tachometer-alt"></i>
                        الإدارة الرئيسية
                    </h3>
                    <div class="menu-items">
                        <a href="#dashboard" class="menu-item active" data-section="dashboard">
                            <div class="item-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <div class="item-content">
                                <h4>لوحة التحكم</h4>
                                <p>الصفحة الرئيسية والإحصائيات</p>
                            </div>
                        </a>

                        <a href="#products" class="menu-item" data-section="products">
                            <div class="item-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="item-content">
                                <h4>إدارة المنتجات</h4>
                                <p>إضافة وتعديل المنتجات</p>
                            </div>
                        </a>

                        <a href="#sales" class="menu-item" data-section="sales">
                            <div class="item-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="item-content">
                                <h4>المبيعات</h4>
                                <p>تسجيل وإدارة المبيعات</p>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="menu-section">
                    <h3 class="section-title">
                        <i class="fas fa-tags"></i>
                        العروض والتسويق
                    </h3>
                    <div class="menu-items">
                        <a href="#offers" class="menu-item" data-section="offers">
                            <div class="item-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="item-content">
                                <h4>العروض والخصومات</h4>
                                <p>إنشاء وإدارة العروض</p>
                            </div>
                        </a>

                        <a href="#customers" class="menu-item" data-section="customers">
                            <div class="item-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="item-content">
                                <h4>العملاء</h4>
                                <p>إدارة بيانات العملاء</p>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="menu-section">
                    <h3 class="section-title">
                        <i class="fas fa-chart-line"></i>
                        التقارير والتحليلات
                    </h3>
                    <div class="menu-items">
                        <a href="#reports" class="menu-item" data-section="reports">
                            <div class="item-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="item-content">
                                <h4>التقارير</h4>
                                <p>تقارير المبيعات والأرباح</p>
                            </div>
                        </a>

                        <a href="#analytics" class="menu-item" data-section="analytics">
                            <div class="item-icon">
                                <i class="fas fa-analytics"></i>
                            </div>
                            <div class="item-content">
                                <h4>التحليلات</h4>
                                <p>تحليل الأداء والاتجاهات</p>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="menu-section">
                    <h3 class="section-title">
                        <i class="fas fa-cog"></i>
                        الإعدادات والأدوات
                    </h3>
                    <div class="menu-items">
                        <a href="#settings" class="menu-item" data-section="settings">
                            <div class="item-icon">
                                <i class="fas fa-sliders-h"></i>
                            </div>
                            <div class="item-content">
                                <h4>إعدادات النظام</h4>
                                <p>تخصيص النظام والإعدادات</p>
                            </div>
                        </a>

                        <a href="#backup" class="menu-item" data-section="backup">
                            <div class="item-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="item-content">
                                <h4>النسخ الاحتياطي</h4>
                                <p>حفظ واستعادة البيانات</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main">
        <!-- Dashboard Section -->
        <section id="dashboard" class="section active">
            <div class="container">
                <h2 class="section-title">لوحة التحكم</h2>
                
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-products">0</h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-sales">0</h3>
                            <p>إجمالي المبيعات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-revenue">0 ج.م</h3>
                            <p>إجمالي الإيرادات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="active-offers">0</h3>
                            <p>العروض النشطة</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="recent-activities">
                    <h3>النشاطات الأخيرة</h3>
                    <div class="activities-list" id="recent-activities">
                        <!-- Activities will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Section -->
        <section id="products" class="section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">إدارة المنتجات</h2>
                    <button class="btn btn-primary" id="add-product-btn">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </button>
                </div>

                <!-- Search and Filter -->
                <div class="search-filter">
                    <div class="search-box">
                        <input type="text" id="product-search" placeholder="البحث في المنتجات...">
                        <i class="fas fa-search"></i>
                    </div>
                    <select id="category-filter">
                        <option value="">جميع الفئات</option>
                        <option value="seat-upholstery">فرش المقاعد الكامل</option>
                        <option value="floor-mats">دواسات الأرضية</option>
                        <option value="leather-full-set">دواسات جلد كاملة للسيارة</option>
                        <option value="car-covers">أغطية السيارات</option>
                        <option value="steering-covers">أغطية المقود</option>
                        <option value="seat-covers">أغطية المقاعد</option>
                        <option value="dashboard-covers">أغطية التابلوه</option>
                        <option value="trunk-mats">سجاد الشنطة</option>
                        <option value="door-panels">تنجيد الأبواب</option>
                        <option value="ceiling-upholstery">تنجيد السقف</option>
                        <option value="accessories">إكسسوارات متنوعة</option>
                    </select>
                </div>

                <!-- Products Grid -->
                <div class="products-grid" id="products-grid">
                    <!-- Products will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Sales Section -->
        <section id="sales" class="section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">إدارة المبيعات</h2>
                    <button class="btn btn-primary" id="new-sale-btn">
                        <i class="fas fa-plus"></i> عملية بيع جديدة
                    </button>
                </div>

                <!-- Sales List -->
                <div class="sales-table-container">
                    <table class="sales-table" id="sales-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>العميل</th>
                                <th>المنتجات</th>
                                <th>المبلغ الإجمالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Sales will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Offers Section -->
        <section id="offers" class="section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">إدارة العروض</h2>
                    <button class="btn btn-primary" id="add-offer-btn">
                        <i class="fas fa-plus"></i> إضافة عرض جديد
                    </button>
                </div>

                <!-- Offers Grid -->
                <div class="offers-grid" id="offers-grid">
                    <!-- Offers will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports" class="section">
            <div class="container">
                <h2 class="section-title">التقارير والإحصائيات</h2>
                
                <!-- Report Filters -->
                <div class="report-filters">
                    <select id="report-type">
                        <option value="sales">تقرير المبيعات</option>
                        <option value="products">تقرير المنتجات</option>
                        <option value="revenue">تقرير الإيرادات</option>
                    </select>
                    <input type="date" id="date-from">
                    <input type="date" id="date-to">
                    <button class="btn btn-secondary" id="generate-report">إنشاء التقرير</button>
                </div>

                <!-- Charts Container -->
                <div class="charts-container">
                    <div class="chart-card">
                        <h3>مبيعات الشهر</h3>
                        <canvas id="sales-chart"></canvas>
                    </div>
                    <div class="chart-card">
                        <h3>أفضل المنتجات مبيعاً</h3>
                        <canvas id="products-chart"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="section">
            <div class="container">
                <h2 class="section-title">إعدادات النظام</h2>

                <!-- Settings Navigation -->
                <div class="settings-nav">
                    <button class="settings-tab-btn active" data-tab="general">الإعدادات العامة</button>
                    <button class="settings-tab-btn" data-tab="categories">إدارة الفئات</button>
                    <button class="settings-tab-btn" data-tab="users">إدارة المستخدمين</button>
                    <button class="settings-tab-btn" data-tab="backup">النسخ الاحتياطي</button>
                    <button class="settings-tab-btn" data-tab="appearance">المظهر</button>
                </div>

                <!-- General Settings -->
                <div id="general-settings" class="settings-content active">
                    <div class="settings-card">
                        <h3>معلومات المحل</h3>
                        <form id="shop-info-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="shop-name">اسم المحل</label>
                                    <input type="text" id="shop-name" value="أورجينال فرش السيارات">
                                </div>
                                <div class="form-group">
                                    <label for="shop-phone">رقم الهاتف</label>
                                    <input type="tel" id="shop-phone" value="01000000000">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="shop-address">العنوان</label>
                                    <textarea id="shop-address" rows="3">القاهرة، مصر</textarea>
                                </div>
                                <div class="form-group">
                                    <label for="shop-email">البريد الإلكتروني</label>
                                    <input type="email" id="shop-email" value="<EMAIL>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="shop-logo">شعار المحل</label>
                                <input type="file" id="shop-logo" accept="image/*">
                            </div>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </form>
                    </div>

                    <div class="settings-card">
                        <h3>إعدادات النظام</h3>
                        <form id="system-settings-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="currency">العملة</label>
                                    <select id="currency">
                                        <option value="EGP">جنيه مصري (ج.م)</option>
                                        <option value="USD">دولار أمريكي ($)</option>
                                        <option value="EUR">يورو (€)</option>
                                        <option value="SAR">ريال سعودي (ر.س)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="tax-rate">معدل الضريبة (%)</label>
                                    <input type="number" id="tax-rate" value="14" min="0" max="100" step="0.1">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="low-stock-alert">تنبيه المخزون المنخفض</label>
                                    <input type="number" id="low-stock-alert" value="5" min="1">
                                </div>
                                <div class="form-group">
                                    <label for="auto-backup">النسخ الاحتياطي التلقائي</label>
                                    <select id="auto-backup">
                                        <option value="daily">يومياً</option>
                                        <option value="weekly">أسبوعياً</option>
                                        <option value="monthly">شهرياً</option>
                                        <option value="disabled">معطل</option>
                                    </select>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                        </form>
                    </div>
                </div>

                <!-- Categories Management -->
                <div id="categories-settings" class="settings-content">
                    <div class="settings-card">
                        <div class="card-header">
                            <h3>إدارة فئات المنتجات</h3>
                            <button class="btn btn-primary" id="add-category-btn">
                                <i class="fas fa-plus"></i> إضافة فئة جديدة
                            </button>
                        </div>
                        <div class="categories-grid" id="categories-grid">
                            <!-- Categories will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Users Management -->
                <div id="users-settings" class="settings-content">
                    <div class="settings-card">
                        <div class="card-header">
                            <h3>إدارة المستخدمين</h3>
                            <button class="btn btn-primary" id="add-user-btn">
                                <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                            </button>
                        </div>
                        <div class="users-table-container">
                            <table class="users-table" id="users-table">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>اسم المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>آخر دخول</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Users will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Backup Settings -->
                <div id="backup-settings" class="settings-content">
                    <div class="settings-card">
                        <h3>النسخ الاحتياطي واستعادة البيانات</h3>
                        <div class="backup-actions">
                            <div class="backup-item">
                                <div class="backup-info">
                                    <h4>إنشاء نسخة احتياطية</h4>
                                    <p>قم بتصدير جميع بيانات النظام في ملف واحد</p>
                                </div>
                                <button class="btn btn-primary" id="export-data-btn">
                                    <i class="fas fa-download"></i> تصدير البيانات
                                </button>
                            </div>

                            <div class="backup-item">
                                <div class="backup-info">
                                    <h4>استعادة البيانات</h4>
                                    <p>استيراد البيانات من ملف نسخة احتياطية</p>
                                </div>
                                <div class="import-controls">
                                    <input type="file" id="import-file" accept=".json" style="display: none;">
                                    <button class="btn btn-secondary" onclick="document.getElementById('import-file').click()">
                                        <i class="fas fa-upload"></i> اختيار ملف
                                    </button>
                                </div>
                            </div>

                            <div class="backup-item">
                                <div class="backup-info">
                                    <h4>مسح جميع البيانات</h4>
                                    <p class="text-danger">حذف جميع البيانات نهائياً (لا يمكن التراجع)</p>
                                </div>
                                <button class="btn btn-danger" id="clear-all-data-btn">
                                    <i class="fas fa-trash"></i> مسح البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Appearance Settings -->
                <div id="appearance-settings" class="settings-content">
                    <div class="settings-card">
                        <h3>إعدادات المظهر</h3>
                        <form id="appearance-form">
                            <div class="form-group">
                                <label for="theme-color">لون النظام الأساسي</label>
                                <div class="color-picker-group">
                                    <input type="color" id="theme-color" value="#667eea">
                                    <div class="color-presets">
                                        <button type="button" class="color-preset" data-color="#667eea" style="background: #667eea;"></button>
                                        <button type="button" class="color-preset" data-color="#28a745" style="background: #28a745;"></button>
                                        <button type="button" class="color-preset" data-color="#dc3545" style="background: #dc3545;"></button>
                                        <button type="button" class="color-preset" data-color="#ffc107" style="background: #ffc107;"></button>
                                        <button type="button" class="color-preset" data-color="#6f42c1" style="background: #6f42c1;"></button>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="font-size">حجم الخط</label>
                                <select id="font-size">
                                    <option value="small">صغير</option>
                                    <option value="medium" selected>متوسط</option>
                                    <option value="large">كبير</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-container">
                                    <input type="checkbox" id="dark-mode">
                                    <span class="checkmark"></span>
                                    الوضع الليلي
                                </label>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-container">
                                    <input type="checkbox" id="animations" checked>
                                    <span class="checkmark"></span>
                                    تفعيل الحركات والتأثيرات
                                </label>
                            </div>

                            <button type="submit" class="btn btn-primary">تطبيق التغييرات</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <!-- Product Modal -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="product-modal-title">إضافة منتج جديد</h3>
                <span class="close" id="close-product-modal">&times;</span>
            </div>
            <form id="product-form">
                <div class="form-group">
                    <label for="product-name">اسم المنتج</label>
                    <input type="text" id="product-name" required>
                </div>
                <div class="form-group">
                    <label for="product-category">الفئة</label>
                    <select id="product-category" required>
                        <option value="">اختر الفئة</option>
                        <option value="seat-upholstery">فرش المقاعد الكامل</option>
                        <option value="floor-mats">دواسات الأرضية</option>
                        <option value="leather-full-set">دواسات جلد كاملة للسيارة</option>
                        <option value="car-covers">أغطية السيارات</option>
                        <option value="steering-covers">أغطية المقود</option>
                        <option value="seat-covers">أغطية المقاعد</option>
                        <option value="dashboard-covers">أغطية التابلوه</option>
                        <option value="trunk-mats">سجاد الشنطة</option>
                        <option value="door-panels">تنجيد الأبواب</option>
                        <option value="ceiling-upholstery">تنجيد السقف</option>
                        <option value="accessories">إكسسوارات متنوعة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="product-price">السعر</label>
                    <input type="number" id="product-price" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="product-quantity">الكمية</label>
                    <input type="number" id="product-quantity" required>
                </div>
                <div class="form-group">
                    <label for="product-description">الوصف</label>
                    <textarea id="product-description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="product-image">صورة المنتج</label>
                    <input type="file" id="product-image" accept="image/*">
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary" id="cancel-product">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loading" class="loading">
        <div class="spinner"></div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
