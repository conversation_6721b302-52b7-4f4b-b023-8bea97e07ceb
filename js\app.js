// Car Upholstery Management System
class CarUpholsterySystem {
    constructor() {
        this.products = JSON.parse(localStorage.getItem('products')) || [];
        this.sales = JSON.parse(localStorage.getItem('sales')) || [];
        this.offers = JSON.parse(localStorage.getItem('offers')) || [];
        this.currentSection = 'dashboard';
        this.editingProductId = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateDashboard();
        this.renderProducts();
        this.renderSales();
        this.renderOffers();
        this.addSampleData();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.closest('.nav-link').dataset.section;
                this.showSection(section);
            });
        });

        // Product Modal
        document.getElementById('add-product-btn').addEventListener('click', () => {
            this.openProductModal();
        });

        document.getElementById('close-product-modal').addEventListener('click', () => {
            this.closeProductModal();
        });

        document.getElementById('cancel-product').addEventListener('click', () => {
            this.closeProductModal();
        });

        document.getElementById('product-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveProduct();
        });

        // Search and Filter
        document.getElementById('product-search').addEventListener('input', (e) => {
            this.filterProducts(e.target.value);
        });

        document.getElementById('category-filter').addEventListener('change', (e) => {
            this.filterProductsByCategory(e.target.value);
        });

        // Sales
        document.getElementById('new-sale-btn').addEventListener('click', () => {
            this.openSaleModal();
        });

        // Offers
        document.getElementById('add-offer-btn').addEventListener('click', () => {
            this.openOfferModal();
        });

        // Reports
        document.getElementById('generate-report').addEventListener('click', () => {
            this.generateReport();
        });

        // Modal close on outside click
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });

        // Show selected section
        document.getElementById(sectionName).classList.add('active');

        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        this.currentSection = sectionName;

        // Update section-specific data
        if (sectionName === 'dashboard') {
            this.updateDashboard();
        } else if (sectionName === 'reports') {
            this.initializeCharts();
        }
    }

    // Product Management
    openProductModal(productId = null) {
        const modal = document.getElementById('product-modal');
        const title = document.getElementById('product-modal-title');
        const form = document.getElementById('product-form');

        if (productId) {
            const product = this.products.find(p => p.id === productId);
            title.textContent = 'تعديل المنتج';
            this.fillProductForm(product);
            this.editingProductId = productId;
        } else {
            title.textContent = 'إضافة منتج جديد';
            form.reset();
            this.editingProductId = null;
        }

        modal.style.display = 'block';
    }

    closeProductModal() {
        document.getElementById('product-modal').style.display = 'none';
        document.getElementById('product-form').reset();
        this.editingProductId = null;
    }

    fillProductForm(product) {
        document.getElementById('product-name').value = product.name;
        document.getElementById('product-category').value = product.category;
        document.getElementById('product-price').value = product.price;
        document.getElementById('product-quantity').value = product.quantity;
        document.getElementById('product-description').value = product.description || '';
    }

    saveProduct() {
        const formData = new FormData(document.getElementById('product-form'));
        const imageFile = document.getElementById('product-image').files[0];

        const product = {
            id: this.editingProductId || Date.now().toString(),
            name: document.getElementById('product-name').value,
            category: document.getElementById('product-category').value,
            price: parseFloat(document.getElementById('product-price').value),
            quantity: parseInt(document.getElementById('product-quantity').value),
            description: document.getElementById('product-description').value,
            image: imageFile ? URL.createObjectURL(imageFile) : 'https://via.placeholder.com/300x200?text=منتج',
            createdAt: this.editingProductId ? 
                this.products.find(p => p.id === this.editingProductId).createdAt : 
                new Date().toISOString()
        };

        if (this.editingProductId) {
            const index = this.products.findIndex(p => p.id === this.editingProductId);
            this.products[index] = product;
        } else {
            this.products.push(product);
        }

        this.saveToStorage();
        this.renderProducts();
        this.updateDashboard();
        this.closeProductModal();
        this.showNotification('تم حفظ المنتج بنجاح');
    }

    deleteProduct(productId) {
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            this.products = this.products.filter(p => p.id !== productId);
            this.saveToStorage();
            this.renderProducts();
            this.updateDashboard();
            this.showNotification('تم حذف المنتج بنجاح');
        }
    }

    renderProducts() {
        const grid = document.getElementById('products-grid');
        grid.innerHTML = '';

        this.products.forEach(product => {
            const productCard = this.createProductCard(product);
            grid.appendChild(productCard);
        });
    }

    createProductCard(product) {
        const card = document.createElement('div');
        card.className = 'product-card';
        card.innerHTML = `
            <img src="${product.image}" alt="${product.name}" class="product-image">
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-category">${this.getCategoryName(product.category)}</p>
                <p class="product-price">${product.price} ج.م</p>
                <p class="product-quantity">الكمية: ${product.quantity}</p>
                <div class="product-actions">
                    <button class="btn btn-primary btn-sm" onclick="app.openProductModal('${product.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="app.deleteProduct('${product.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
        return card;
    }

    getCategoryName(category) {
        const categories = {
            'seat-covers': 'أغطية المقاعد',
            'floor-mats': 'سجاد الأرضية',
            'steering-covers': 'أغطية المقود',
            'accessories': 'إكسسوارات'
        };
        return categories[category] || category;
    }

    filterProducts(searchTerm) {
        const cards = document.querySelectorAll('.product-card');
        cards.forEach(card => {
            const name = card.querySelector('.product-name').textContent.toLowerCase();
            const category = card.querySelector('.product-category').textContent.toLowerCase();
            
            if (name.includes(searchTerm.toLowerCase()) || category.includes(searchTerm.toLowerCase())) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    filterProductsByCategory(category) {
        const cards = document.querySelectorAll('.product-card');
        cards.forEach(card => {
            if (!category || card.querySelector('.product-category').textContent === this.getCategoryName(category)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    // Dashboard Updates
    updateDashboard() {
        document.getElementById('total-products').textContent = this.products.length;
        document.getElementById('total-sales').textContent = this.sales.length;
        
        const totalRevenue = this.sales.reduce((sum, sale) => sum + sale.total, 0);
        document.getElementById('total-revenue').textContent = `${totalRevenue.toFixed(2)} ج.م`;
        
        const activeOffers = this.offers.filter(offer => new Date(offer.endDate) > new Date()).length;
        document.getElementById('active-offers').textContent = activeOffers;

        this.renderRecentActivities();
    }

    renderRecentActivities() {
        const container = document.getElementById('recent-activities');
        container.innerHTML = '';

        // Combine and sort recent activities
        const activities = [
            ...this.sales.slice(-5).map(sale => ({
                type: 'sale',
                text: `تم بيع ${sale.items.length} منتج للعميل ${sale.customerName}`,
                time: new Date(sale.date).toLocaleDateString('ar-EG'),
                icon: 'fas fa-shopping-cart'
            })),
            ...this.products.slice(-3).map(product => ({
                type: 'product',
                text: `تم إضافة منتج جديد: ${product.name}`,
                time: new Date(product.createdAt).toLocaleDateString('ar-EG'),
                icon: 'fas fa-box'
            }))
        ].slice(-5);

        activities.forEach(activity => {
            const activityElement = document.createElement('div');
            activityElement.className = 'activity-item';
            activityElement.innerHTML = `
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-text">${activity.text}</div>
                    <div class="activity-time">${activity.time}</div>
                </div>
            `;
            container.appendChild(activityElement);
        });
    }

    // Utility Functions
    saveToStorage() {
        try {
            localStorage.setItem('products', JSON.stringify(this.products));
            localStorage.setItem('sales', JSON.stringify(this.sales));
            localStorage.setItem('offers', JSON.stringify(this.offers));
            localStorage.setItem('lastBackup', new Date().toISOString());
        } catch (error) {
            this.showNotification('خطأ في حفظ البيانات: ' + error.message, 'error');
        }
    }

    showNotification(message, type = 'success') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create new notification
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Backup and Restore Functions
    exportData() {
        const data = {
            products: this.products,
            sales: this.sales,
            offers: this.offers,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };

        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `car-upholstery-backup-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        URL.revokeObjectURL(url);
        this.showNotification('تم تصدير البيانات بنجاح');
    }

    importData(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);

                if (data.products && data.sales && data.offers) {
                    if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                        this.products = data.products;
                        this.sales = data.sales;
                        this.offers = data.offers;

                        this.saveToStorage();
                        this.renderProducts();
                        this.renderSales();
                        this.renderOffers();
                        this.updateDashboard();

                        this.showNotification('تم استيراد البيانات بنجاح');
                    }
                } else {
                    this.showNotification('ملف البيانات غير صحيح', 'error');
                }
            } catch (error) {
                this.showNotification('خطأ في قراءة الملف: ' + error.message, 'error');
            }
        };
        reader.readAsText(file);
    }

    // Advanced Search Functions
    advancedProductSearch(query, category, priceRange) {
        return this.products.filter(product => {
            const matchesQuery = !query ||
                product.name.toLowerCase().includes(query.toLowerCase()) ||
                product.description.toLowerCase().includes(query.toLowerCase());

            const matchesCategory = !category || product.category === category;

            const matchesPrice = !priceRange ||
                (product.price >= priceRange.min && product.price <= priceRange.max);

            return matchesQuery && matchesCategory && matchesPrice;
        });
    }

    // Data Validation
    validateProduct(product) {
        const errors = [];

        if (!product.name || product.name.trim().length < 2) {
            errors.push('اسم المنتج يجب أن يكون أكثر من حرفين');
        }

        if (!product.category) {
            errors.push('يجب اختيار فئة المنتج');
        }

        if (!product.price || product.price <= 0) {
            errors.push('سعر المنتج يجب أن يكون أكبر من صفر');
        }

        if (!product.quantity || product.quantity < 0) {
            errors.push('كمية المنتج يجب أن تكون صفر أو أكثر');
        }

        return errors;
    }

    // Performance Monitoring
    logPerformance(action, startTime) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        console.log(`${action} took ${duration.toFixed(2)} milliseconds`);

        if (duration > 1000) {
            console.warn(`Slow operation detected: ${action}`);
        }
    }

    // Sales Management
    openSaleModal() {
        // Create a simple sale modal
        const saleModal = this.createSaleModal();
        document.body.appendChild(saleModal);
    }

    createSaleModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'sale-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>عملية بيع جديدة</h3>
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                </div>
                <form id="sale-form">
                    <div style="padding: 2rem;">
                        <div class="form-group">
                            <label for="customer-name">اسم العميل</label>
                            <input type="text" id="customer-name" required>
                        </div>
                        <div class="form-group">
                            <label for="customer-phone">رقم الهاتف</label>
                            <input type="tel" id="customer-phone">
                        </div>
                        <div class="form-group">
                            <label>المنتجات</label>
                            <div id="sale-products">
                                ${this.products.map(product => `
                                    <div class="product-select-item">
                                        <input type="checkbox" id="product-${product.id}" value="${product.id}">
                                        <label for="product-${product.id}">${product.name} - ${product.price} ج.م</label>
                                        <input type="number" min="1" max="${product.quantity}" placeholder="الكمية" style="width: 80px; margin-right: 10px;">
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">إتمام البيع</button>
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">إلغاء</button>
                        </div>
                    </div>
                </form>
            </div>
        `;

        modal.querySelector('#sale-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.processSale(modal);
        });

        modal.style.display = 'block';
        return modal;
    }

    processSale(modal) {
        const customerName = modal.querySelector('#customer-name').value;
        const customerPhone = modal.querySelector('#customer-phone').value;
        const selectedProducts = [];

        modal.querySelectorAll('.product-select-item').forEach(item => {
            const checkbox = item.querySelector('input[type="checkbox"]');
            const quantityInput = item.querySelector('input[type="number"]');

            if (checkbox.checked && quantityInput.value) {
                const productId = checkbox.value;
                const quantity = parseInt(quantityInput.value);
                const product = this.products.find(p => p.id === productId);

                if (product && quantity <= product.quantity) {
                    selectedProducts.push({
                        productId,
                        name: product.name,
                        price: product.price,
                        quantity,
                        total: product.price * quantity
                    });

                    // Update product quantity
                    product.quantity -= quantity;
                }
            }
        });

        if (selectedProducts.length > 0) {
            const sale = {
                id: Date.now().toString(),
                customerName,
                customerPhone,
                items: selectedProducts,
                total: selectedProducts.reduce((sum, item) => sum + item.total, 0),
                date: new Date().toISOString(),
                status: 'completed'
            };

            this.sales.push(sale);
            this.saveToStorage();
            this.renderSales();
            this.updateDashboard();
            modal.remove();
            this.showNotification('تم إتمام البيع بنجاح');
        } else {
            alert('يرجى اختيار منتج واحد على الأقل');
        }
    }

    renderSales() {
        const tbody = document.querySelector('#sales-table tbody');
        tbody.innerHTML = '';

        this.sales.forEach(sale => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>#${sale.id.slice(-6)}</td>
                <td>${new Date(sale.date).toLocaleDateString('ar-EG')}</td>
                <td>${sale.customerName}</td>
                <td>${sale.items.length} منتج</td>
                <td>${sale.total.toFixed(2)} ج.م</td>
                <td><span class="status-badge status-${sale.status}">${this.getStatusText(sale.status)}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="app.viewSaleDetails('${sale.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="app.printInvoice('${sale.id}')">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    getStatusText(status) {
        const statusTexts = {
            'completed': 'مكتمل',
            'pending': 'معلق',
            'cancelled': 'ملغي'
        };
        return statusTexts[status] || status;
    }

    viewSaleDetails(saleId) {
        const sale = this.sales.find(s => s.id === saleId);
        if (sale) {
            alert(`تفاصيل الفاتورة #${sale.id.slice(-6)}\n\nالعميل: ${sale.customerName}\nالمنتجات: ${sale.items.map(item => `${item.name} (${item.quantity})`).join(', ')}\nالإجمالي: ${sale.total} ج.م`);
        }
    }

    printInvoice(saleId) {
        const sale = this.sales.find(s => s.id === saleId);
        if (sale) {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>فاتورة #${sale.id.slice(-6)}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .invoice-details { margin-bottom: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; }
                        .total { font-weight: bold; font-size: 18px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>أورجينال فرش السيارات</h1>
                        <h2>فاتورة #${sale.id.slice(-6)}</h2>
                    </div>
                    <div class="invoice-details">
                        <p><strong>العميل:</strong> ${sale.customerName}</p>
                        <p><strong>التاريخ:</strong> ${new Date(sale.date).toLocaleDateString('ar-EG')}</p>
                        <p><strong>الهاتف:</strong> ${sale.customerPhone || 'غير محدد'}</p>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sale.items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.price} ج.م</td>
                                    <td>${item.quantity}</td>
                                    <td>${item.total} ج.م</td>
                                </tr>
                            `).join('')}
                        </tbody>
                        <tfoot>
                            <tr class="total">
                                <td colspan="3">الإجمالي</td>
                                <td>${sale.total} ج.م</td>
                            </tr>
                        </tfoot>
                    </table>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    }

    // Offers Management
    openOfferModal() {
        const modal = this.createOfferModal();
        document.body.appendChild(modal);
    }

    createOfferModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'offer-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>إضافة عرض جديد</h3>
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                </div>
                <form id="offer-form">
                    <div style="padding: 2rem;">
                        <div class="form-group">
                            <label for="offer-title">عنوان العرض</label>
                            <input type="text" id="offer-title" required>
                        </div>
                        <div class="form-group">
                            <label for="offer-description">وصف العرض</label>
                            <textarea id="offer-description" rows="3" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="offer-discount">نسبة الخصم (%)</label>
                            <input type="number" id="offer-discount" min="1" max="100" required>
                        </div>
                        <div class="form-group">
                            <label for="offer-start-date">تاريخ البداية</label>
                            <input type="date" id="offer-start-date" required>
                        </div>
                        <div class="form-group">
                            <label for="offer-end-date">تاريخ النهاية</label>
                            <input type="date" id="offer-end-date" required>
                        </div>
                        <div class="form-group">
                            <label>المنتجات المشمولة</label>
                            <div id="offer-products">
                                ${this.products.map(product => `
                                    <div class="product-select-item">
                                        <input type="checkbox" id="offer-product-${product.id}" value="${product.id}">
                                        <label for="offer-product-${product.id}">${product.name}</label>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">حفظ العرض</button>
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">إلغاء</button>
                        </div>
                    </div>
                </form>
            </div>
        `;

        modal.querySelector('#offer-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveOffer(modal);
        });

        modal.style.display = 'block';
        return modal;
    }

    saveOffer(modal) {
        const title = modal.querySelector('#offer-title').value;
        const description = modal.querySelector('#offer-description').value;
        const discount = parseInt(modal.querySelector('#offer-discount').value);
        const startDate = modal.querySelector('#offer-start-date').value;
        const endDate = modal.querySelector('#offer-end-date').value;

        const selectedProducts = [];
        modal.querySelectorAll('#offer-products input[type="checkbox"]:checked').forEach(checkbox => {
            selectedProducts.push(checkbox.value);
        });

        const offer = {
            id: Date.now().toString(),
            title,
            description,
            discount,
            startDate,
            endDate,
            products: selectedProducts,
            isActive: new Date(startDate) <= new Date() && new Date(endDate) >= new Date(),
            createdAt: new Date().toISOString()
        };

        this.offers.push(offer);
        this.saveToStorage();
        this.renderOffers();
        this.updateDashboard();
        modal.remove();
        this.showNotification('تم حفظ العرض بنجاح');
    }

    renderOffers() {
        const grid = document.getElementById('offers-grid');
        grid.innerHTML = '';

        this.offers.forEach(offer => {
            const offerCard = this.createOfferCard(offer);
            grid.appendChild(offerCard);
        });
    }

    createOfferCard(offer) {
        const card = document.createElement('div');
        card.className = 'offer-card';
        const isActive = new Date(offer.startDate) <= new Date() && new Date(offer.endDate) >= new Date();

        card.innerHTML = `
            <h3 class="offer-title">${offer.title}</h3>
            <div class="offer-discount">${offer.discount}% خصم</div>
            <p class="offer-description">${offer.description}</p>
            <p class="offer-validity">
                صالح من ${new Date(offer.startDate).toLocaleDateString('ar-EG')}
                إلى ${new Date(offer.endDate).toLocaleDateString('ar-EG')}
            </p>
            <div class="offer-status">
                <span class="status-badge ${isActive ? 'status-completed' : 'status-cancelled'}">
                    ${isActive ? 'نشط' : 'منتهي الصلاحية'}
                </span>
            </div>
            <div class="offer-actions">
                <button class="btn btn-danger btn-sm" onclick="app.deleteOffer('${offer.id}')">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        `;
        return card;
    }

    deleteOffer(offerId) {
        if (confirm('هل أنت متأكد من حذف هذا العرض؟')) {
            this.offers = this.offers.filter(o => o.id !== offerId);
            this.saveToStorage();
            this.renderOffers();
            this.updateDashboard();
            this.showNotification('تم حذف العرض بنجاح');
        }
    }

    // Reports and Charts
    generateReport() {
        const reportType = document.getElementById('report-type').value;
        const dateFrom = document.getElementById('date-from').value;
        const dateTo = document.getElementById('date-to').value;

        let reportData = [];

        switch (reportType) {
            case 'sales':
                reportData = this.generateSalesReport(dateFrom, dateTo);
                break;
            case 'products':
                reportData = this.generateProductsReport();
                break;
            case 'revenue':
                reportData = this.generateRevenueReport(dateFrom, dateTo);
                break;
        }

        this.displayReport(reportData, reportType);
    }

    generateSalesReport(dateFrom, dateTo) {
        let filteredSales = this.sales;

        if (dateFrom) {
            filteredSales = filteredSales.filter(sale => new Date(sale.date) >= new Date(dateFrom));
        }
        if (dateTo) {
            filteredSales = filteredSales.filter(sale => new Date(sale.date) <= new Date(dateTo));
        }

        return {
            totalSales: filteredSales.length,
            totalRevenue: filteredSales.reduce((sum, sale) => sum + sale.total, 0),
            averageOrderValue: filteredSales.length > 0 ?
                filteredSales.reduce((sum, sale) => sum + sale.total, 0) / filteredSales.length : 0,
            sales: filteredSales
        };
    }

    generateProductsReport() {
        const productStats = this.products.map(product => {
            const soldQuantity = this.sales.reduce((total, sale) => {
                const saleItem = sale.items.find(item => item.productId === product.id);
                return total + (saleItem ? saleItem.quantity : 0);
            }, 0);

            return {
                ...product,
                soldQuantity,
                remainingQuantity: product.quantity,
                revenue: this.sales.reduce((total, sale) => {
                    const saleItem = sale.items.find(item => item.productId === product.id);
                    return total + (saleItem ? saleItem.total : 0);
                }, 0)
            };
        });

        return productStats.sort((a, b) => b.soldQuantity - a.soldQuantity);
    }

    generateRevenueReport(dateFrom, dateTo) {
        // Group sales by month for revenue chart
        const monthlyRevenue = {};

        this.sales.forEach(sale => {
            const saleDate = new Date(sale.date);
            if ((!dateFrom || saleDate >= new Date(dateFrom)) &&
                (!dateTo || saleDate <= new Date(dateTo))) {
                const monthKey = `${saleDate.getFullYear()}-${saleDate.getMonth() + 1}`;
                monthlyRevenue[monthKey] = (monthlyRevenue[monthKey] || 0) + sale.total;
            }
        });

        return monthlyRevenue;
    }

    displayReport(reportData, reportType) {
        alert(`تم إنشاء تقرير ${reportType} بنجاح!\nيمكنك رؤية التفاصيل في وحدة التحكم.`);
        console.log('Report Data:', reportData);
    }

    initializeCharts() {
        this.createSalesChart();
        this.createProductsChart();
    }

    createSalesChart() {
        const ctx = document.getElementById('sales-chart');
        if (!ctx) return;

        // Get last 6 months data
        const monthlyData = {};
        const last6Months = [];

        for (let i = 5; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
            const monthName = date.toLocaleDateString('ar-EG', { month: 'long' });
            last6Months.push(monthName);
            monthlyData[monthKey] = 0;
        }

        // Calculate sales for each month
        this.sales.forEach(sale => {
            const saleDate = new Date(sale.date);
            const monthKey = `${saleDate.getFullYear()}-${saleDate.getMonth() + 1}`;
            if (monthlyData.hasOwnProperty(monthKey)) {
                monthlyData[monthKey] += sale.total;
            }
        });

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: last6Months,
                datasets: [{
                    label: 'المبيعات (ج.م)',
                    data: Object.values(monthlyData),
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: true
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    createProductsChart() {
        const ctx = document.getElementById('products-chart');
        if (!ctx) return;

        const productSales = this.generateProductsReport().slice(0, 5);

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: productSales.map(p => p.name),
                datasets: [{
                    data: productSales.map(p => p.soldQuantity),
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    addSampleData() {
        if (this.products.length === 0) {
            const sampleProducts = [
                {
                    id: '1',
                    name: 'غطاء مقاعد جلد طبيعي',
                    category: 'seat-covers',
                    price: 1500,
                    quantity: 10,
                    description: 'غطاء مقاعد من الجلد الطبيعي عالي الجودة',
                    image: 'https://via.placeholder.com/300x200?text=غطاء+مقاعد',
                    createdAt: new Date().toISOString()
                },
                {
                    id: '2',
                    name: 'سجادة أرضية مطاطية',
                    category: 'floor-mats',
                    price: 300,
                    quantity: 25,
                    description: 'سجادة أرضية مقاومة للماء والأتربة',
                    image: 'https://via.placeholder.com/300x200?text=سجادة+أرضية',
                    createdAt: new Date().toISOString()
                },
                {
                    id: '3',
                    name: 'غطاء مقود جلد',
                    category: 'steering-covers',
                    price: 200,
                    quantity: 15,
                    description: 'غطاء مقود من الجلد المريح',
                    image: 'https://via.placeholder.com/300x200?text=غطاء+مقود',
                    createdAt: new Date().toISOString()
                }
            ];
            this.products = sampleProducts;
            this.saveToStorage();
        }
    }
}

// Initialize the application
const app = new CarUpholsterySystem();
