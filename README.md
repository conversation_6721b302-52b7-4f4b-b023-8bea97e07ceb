# نظام إدارة أورجينال فرش السيارات

نظام إدارة شامل ومتطور لمحلات فرش السيارات مبني بتقنيات الويب الحديثة (HTML5, CSS3, JavaScript).

## المميزات الرئيسية

### 🔐 نظام المصادقة والأمان
- نظام تسجيل دخول آمن ومتطور
- إدارة المستخدمين والصلاحيات
- أدوار مختلفة (مدير، موظف، أمين صندوق)
- جلسات آمنة مع انتهاء صلاحية تلقائي
- تشفير كلمات المرور

### 🚗 إدارة المنتجات المتقدمة
- فئات احترافية شاملة:
  - فرش المقاعد الكامل
  - دواسات الأرضية
  - دواسات جلد كاملة للسيارة
  - أغطية السيارات
  - أغطية المقود والتابلوه
  - تنجيد الأبواب والسقف
  - سجاد الشنطة
  - إكسسوارات متنوعة
- رفع صور المنتجات مع معاينة
- تتبع المخزون الذكي مع تنبيهات
- بحث وفلترة متقدمة بالفئات والأسعار

### 💰 نظام المبيعات الاحترافي
- واجهة بيع سهلة وسريعة
- إنشاء فواتير احترافية قابلة للطباعة
- تتبع شامل لبيانات العملاء
- حساب الضرائب والخصومات تلقائياً
- تاريخ مفصل لجميع المعاملات

### 🏷️ نظام العروض المتطور
- أنواع متعددة من العروض:
  - خصم بالنسبة المئوية
  - خصم بمبلغ ثابت
  - عروض "اشتري واحصل على"
  - عروض الحزم
- تحديد نطاق العروض (جميع المنتجات، فئات محددة، منتجات محددة)
- إعدادات متقدمة (حد أدنى للشراء، حد استخدام، أولوية)
- تطبيق تلقائي للعروض
- متابعة استخدام العروض

### 📊 التقارير والإحصائيات التفاعلية
- رسوم بيانية متقدمة مع Chart.js
- تقارير المبيعات بفترات زمنية مخصصة
- إحصائيات المنتجات الأكثر مبيعاً
- تقارير الإيرادات والأرباح
- تصدير التقارير بصيغ متعددة

### ⚙️ نظام الإعدادات الشامل
- إعدادات المحل (الاسم، العنوان، الهاتف، الشعار)
- إعدادات النظام (العملة، الضرائب، تنبيهات المخزون)
- إدارة فئات المنتجات
- إدارة المستخدمين والصلاحيات
- النسخ الاحتياطي واستعادة البيانات
- إعدادات المظهر (الألوان، الخطوط، الوضع الليلي)

### 🎨 التصميم الاحترافي المتطور
- واجهة مستخدم عصرية مع تأثيرات بصرية متقدمة
- خلفيات متحركة وتدرجات ديناميكية
- تأثيرات hover وانتقالات سلسة
- تصميم متجاوب بالكامل لجميع الأجهزة
- دعم كامل للغة العربية مع خطوط جميلة
- نظام ألوان قابل للتخصيص
- رسوم متحركة وتأثيرات بصرية احترافية

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق مع تأثيرات متقدمة
- **JavaScript ES6+**: المنطق والتفاعل
- **Chart.js**: الرسوم البيانية
- **Font Awesome**: الأيقونات
- **Google Fonts**: خطوط عربية جميلة
- **Local Storage**: حفظ البيانات محلياً

## كيفية التشغيل

1. **تحميل الملفات**
   ```bash
   git clone [repository-url]
   cd original-car-upholstery-system
   ```

2. **العرض التجريبي**
   - افتح ملف `demo.html` في المتصفح لمشاهدة العرض التجريبي
   - أو ابدأ مباشرة بفتح `login.html`

3. **تشغيل النظام**
   - استخدم خادم محلي للحصول على أفضل أداء:
   ```bash
   # باستخدام Python
   python -m http.server 8000

   # باستخدام Node.js
   npx serve .

   # باستخدام PHP
   php -S localhost:8000
   ```

4. **الوصول للنظام**
   - افتح المتصفح وانتقل إلى `http://localhost:8000/demo.html`
   - أو انتقل مباشرة إلى `http://localhost:8000/login.html`

5. **بيانات تسجيل الدخول الافتراضية**
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

## هيكل المشروع

```
original-car-upholstery-system/
├── demo.html               # صفحة العرض التجريبي
├── login.html              # صفحة تسجيل الدخول
├── index.html              # الصفحة الرئيسية للنظام
├── css/
│   ├── style.css          # ملف التصميم الرئيسي
│   └── login.css          # تصميم صفحة تسجيل الدخول
├── js/
│   ├── app.js             # ملف JavaScript الرئيسي
│   └── auth.js            # نظام المصادقة والأمان
└── README.md              # دليل المشروع
```

## الاستخدام

### إدارة المنتجات
1. انتقل إلى قسم "المنتجات"
2. اضغط على "إضافة منتج جديد"
3. املأ بيانات المنتج (الاسم، الفئة، السعر، الكمية)
4. ارفع صورة المنتج (اختياري)
5. احفظ المنتج

### تسجيل مبيعة
1. انتقل إلى قسم "المبيعات"
2. اضغط على "عملية بيع جديدة"
3. أدخل بيانات العميل
4. اختر المنتجات والكميات
5. أتمم عملية البيع
6. اطبع الفاتورة إذا لزم الأمر

### إنشاء عرض
1. انتقل إلى قسم "العروض"
2. اضغط على "إضافة عرض جديد"
3. حدد تفاصيل العرض ونسبة الخصم
4. اختر المنتجات المشمولة
5. حدد فترة صلاحية العرض

### عرض التقارير
1. انتقل إلى قسم "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية (اختياري)
4. اضغط على "إنشاء التقرير"

## المميزات التقنية

### تخزين البيانات
- استخدام Local Storage لحفظ البيانات محلياً
- إمكانية النسخ الاحتياطي والاستعادة
- عدم الحاجة لقاعدة بيانات خارجية

### الأمان
- التحقق من صحة البيانات المدخلة
- حماية من الأخطاء الشائعة
- رسائل تأكيد للعمليات الحساسة

### الأداء
- تحميل سريع للصفحات
- تحديث فوري للبيانات
- استجابة سريعة للتفاعلات

## التطوير المستقبلي

### مميزات مخططة
- [ ] نظام المستخدمين وتسجيل الدخول
- [ ] قاعدة بيانات خارجية
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع
- [ ] إشعارات تلقائية
- [ ] نسخ احتياطي سحابي

### تحسينات تقنية
- [ ] Progressive Web App (PWA)
- [ ] وضع العمل بدون إنترنت
- [ ] تحسين الأداء
- [ ] اختبارات تلقائية

## الدعم والمساعدة

### المتطلبات
- متصفح حديث يدعم HTML5 و CSS3 و JavaScript ES6+
- دقة شاشة 1024x768 أو أعلى (للاستخدام الأمثل)

### المشاكل الشائعة
1. **البيانات لا تحفظ**: تأكد من تفعيل Local Storage في المتصفح
2. **الصور لا تظهر**: تأكد من رفع صور بصيغة مدعومة (JPG, PNG, GIF)
3. **التصميم لا يظهر بشكل صحيح**: تأكد من تحميل ملفات CSS بشكل صحيح

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

---

**تم تطوير هذا النظام خصيصاً لمحلات فرش السيارات لتسهيل إدارة الأعمال وزيادة الكفاءة.**
