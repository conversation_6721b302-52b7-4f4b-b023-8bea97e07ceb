# نظام إدارة أورجينال فرش السيارات

نظام إدارة شامل ومتطور لمحلات فرش السيارات مبني بتقنيات الويب الحديثة (HTML5, CSS3, JavaScript).

## المميزات الرئيسية

### 🚗 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- تصنيف المنتجات (أغطية مقاعد، سجاد أرضية، أغطية مقود، إكسسوارات)
- رفع صور المنتجات
- تتبع المخزون والكميات
- بحث وفلترة متقدمة

### 💰 إدارة المبيعات
- تسجيل عمليات البيع
- إنشاء فواتير احترافية
- طباعة الفواتير
- تتبع بيانات العملاء
- حساب الإجماليات تلقائياً

### 🏷️ نظام العروض والخصومات
- إنشاء عروض خاصة
- تحديد فترات صلاحية العروض
- تطبيق خصومات على منتجات محددة
- متابعة العروض النشطة

### 📊 التقارير والإحصائيات
- تقارير المبيعات الشهرية
- إحصائيات المنتجات الأكثر مبيعاً
- تقارير الإيرادات
- رسوم بيانية تفاعلية
- تصدير التقارير

### 🎨 التصميم
- واجهة مستخدم عصرية ومتجاوبة
- دعم اللغة العربية بالكامل
- تصميم متوافق مع جميع الأجهزة
- ألوان وتأثيرات بصرية جذابة

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق مع تأثيرات متقدمة
- **JavaScript ES6+**: المنطق والتفاعل
- **Chart.js**: الرسوم البيانية
- **Font Awesome**: الأيقونات
- **Google Fonts**: خطوط عربية جميلة
- **Local Storage**: حفظ البيانات محلياً

## كيفية التشغيل

1. **تحميل الملفات**
   ```bash
   git clone [repository-url]
   cd original-car-upholstery-system
   ```

2. **فتح النظام**
   - افتح ملف `index.html` في المتصفح
   - أو استخدم خادم محلي:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # باستخدام Node.js
   npx serve .
   ```

3. **الوصول للنظام**
   - افتح المتصفح وانتقل إلى `http://localhost:8000`

## هيكل المشروع

```
original-car-upholstery-system/
├── index.html              # الصفحة الرئيسية
├── css/
│   └── style.css          # ملف التصميم الرئيسي
├── js/
│   └── app.js             # ملف JavaScript الرئيسي
└── README.md              # دليل المشروع
```

## الاستخدام

### إدارة المنتجات
1. انتقل إلى قسم "المنتجات"
2. اضغط على "إضافة منتج جديد"
3. املأ بيانات المنتج (الاسم، الفئة، السعر، الكمية)
4. ارفع صورة المنتج (اختياري)
5. احفظ المنتج

### تسجيل مبيعة
1. انتقل إلى قسم "المبيعات"
2. اضغط على "عملية بيع جديدة"
3. أدخل بيانات العميل
4. اختر المنتجات والكميات
5. أتمم عملية البيع
6. اطبع الفاتورة إذا لزم الأمر

### إنشاء عرض
1. انتقل إلى قسم "العروض"
2. اضغط على "إضافة عرض جديد"
3. حدد تفاصيل العرض ونسبة الخصم
4. اختر المنتجات المشمولة
5. حدد فترة صلاحية العرض

### عرض التقارير
1. انتقل إلى قسم "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية (اختياري)
4. اضغط على "إنشاء التقرير"

## المميزات التقنية

### تخزين البيانات
- استخدام Local Storage لحفظ البيانات محلياً
- إمكانية النسخ الاحتياطي والاستعادة
- عدم الحاجة لقاعدة بيانات خارجية

### الأمان
- التحقق من صحة البيانات المدخلة
- حماية من الأخطاء الشائعة
- رسائل تأكيد للعمليات الحساسة

### الأداء
- تحميل سريع للصفحات
- تحديث فوري للبيانات
- استجابة سريعة للتفاعلات

## التطوير المستقبلي

### مميزات مخططة
- [ ] نظام المستخدمين وتسجيل الدخول
- [ ] قاعدة بيانات خارجية
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع
- [ ] إشعارات تلقائية
- [ ] نسخ احتياطي سحابي

### تحسينات تقنية
- [ ] Progressive Web App (PWA)
- [ ] وضع العمل بدون إنترنت
- [ ] تحسين الأداء
- [ ] اختبارات تلقائية

## الدعم والمساعدة

### المتطلبات
- متصفح حديث يدعم HTML5 و CSS3 و JavaScript ES6+
- دقة شاشة 1024x768 أو أعلى (للاستخدام الأمثل)

### المشاكل الشائعة
1. **البيانات لا تحفظ**: تأكد من تفعيل Local Storage في المتصفح
2. **الصور لا تظهر**: تأكد من رفع صور بصيغة مدعومة (JPG, PNG, GIF)
3. **التصميم لا يظهر بشكل صحيح**: تأكد من تحميل ملفات CSS بشكل صحيح

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

---

**تم تطوير هذا النظام خصيصاً لمحلات فرش السيارات لتسهيل إدارة الأعمال وزيادة الكفاءة.**
